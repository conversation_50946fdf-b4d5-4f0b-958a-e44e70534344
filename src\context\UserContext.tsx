import { createContext, use<PERSON>ontext, useEffect, useState, type <PERSON>actNode } from "react";
import { getFromApi, postToApi, type ApiResponse } from "@/lib/api";
import type { User } from "@/types";

interface UserContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  loginUser: (userData: User) => void;
  logoutUser: () => Promise<void>;
  fetchUserProfile: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUserProfile = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const res: ApiResponse<User> = await getFromApi<User>(`/v1/users/me`, true)
      if (res.data != null) {
        console.log("User profile data:", res.data);
        setUser(res.data);
      } else if (res.error) {
        setUser(null);
        setError(res.error.message);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      setUser(null);
      setError("Failed to load user profile.");
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const loginUser = (userData: User) => {
    setUser(userData);
    setError(null);
  };

  const logoutUser = async () => {
    try {
      await postToApi("/v1/auth/logout", {}, true);
    } catch (error) {
      console.error("Error logging out:", error);
    } finally {
      setUser(null);
      setError(null);
    }
  };

  const contextValue: UserContextType = {
    user,
    isLoading,
    error,
    loginUser,
    logoutUser,
    fetchUserProfile,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  )
}

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};