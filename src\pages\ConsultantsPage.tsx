import { useState } from 'react';
import { <PERSON>Header, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Phone, Mail } from 'lucide-react';
import { ConsultantForm } from '@/components/pages/consultant/ConsultantForm';
import { useBusinessContext } from '@/context/BusinessContext';
import { useEffect } from 'react';
import type { IConsultant } from '@/types';

export function ConsultantsPage() {
  const [isConsultantFormDialogOpen, setIsConsultantFormDialogOpen] = useState(false);
  const [editingConsultant, setEditingConsultant] = useState<IConsultant | null>(null);

  const {
    entities: { consultants },
    loading,
    contextError,
    fetchActions: { fetchConsultants },
  } = useBusinessContext();

  useEffect(() => {
    fetchConsultants();
  }, [fetchConsultants]);

  const columns: Column<IConsultant>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value) => (
        <div className="font-medium">{value}</div>
      )
    },
    {
      key: 'email',
      header: 'Email',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-2">
          <Mail className="h-4 w-4 text-muted-foreground" />
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Phone className="h-4 w-4 text-muted-foreground" />
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-muted-foreground">
          {new Date(value).toLocaleDateString()}
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleEdit = (consultant: IConsultant) => {
    setEditingConsultant(consultant);
    setIsConsultantFormDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    // TODO: Implement delete functionality
    console.log('Delete consultant:', id);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Consultants"
        description="Manage consultants and assignment leads"
      >
        <Button onClick={() => {
          setEditingConsultant(null);
          setIsConsultantFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Consultant
        </Button>
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}

        <DataTable
          data={consultants}
          columns={columns}
          searchPlaceholder="Search consultants..."
          onRowClick={(consultant) => console.log('View consultant:', consultant)}
          loading={loading.consultants}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isConsultantFormDialogOpen} onOpenChange={setIsConsultantFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingConsultant ? 'Edit Consultant' : 'Add New Consultant'}
            </DialogTitle>
          </DialogHeader>
          <ConsultantForm
            mode={editingConsultant ? 'edit' : 'create'}
            initialData={editingConsultant || undefined}
            onCancel={() => setIsConsultantFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
