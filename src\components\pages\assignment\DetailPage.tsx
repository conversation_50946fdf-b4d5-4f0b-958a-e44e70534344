import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import {
  ArrowLeft,
  Edit,
  Calendar,
  User,
  Building2,
  Phone,
  Mail,
  MapPin,
  FileText,
  Briefcase,
  Clock,
  Shield,
  AlertCircle,
  CheckCircle2,
  Users,
  CalendarSync,
  HammerIcon,
  UserIcon,
  Currency,
  BanknoteArrowUp,
  BanknoteArrowDown,
  HandCoins,
  Percent
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/custom-ui/loading';
import { useToast } from '@/components/custom-ui/toast';
import { useBusinessContext } from '@/context/BusinessContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import type { IAssignment } from '@/types';
import { AssignmentForm } from './AssignmentForm';
import { useAssignmentOperations } from './useAssignmentOperations';
import { AssignmentErrorState } from '@/components/error/AssignmentErrorState';
import { CategoryTabs } from './CategoryTabs';
import { CommentsSection } from '../comment/CommentsSection';
import type { AssignmentSummary } from '@/pages/types/assignment';
import { ROMForm } from './ROMForm';
import { RoleTabs } from './RoleTabs';
import { DocumentsView } from './DocumentsView';


export function AssignmentDetailPage() {
  const [assignment, setAssignment] = useState<IAssignment | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isROMDialogOpen, setIsROMDialogOpen] = useState(false);
  const [isDocumentsDialogOpen, setIsDocumentsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assignmentSummary, setAssignmentSummary] = useState<AssignmentSummary | null>(null);

  const navigate = useNavigate();
  const { addToast } = useToast();
  const { id } = useParams<{ id: string }>();
  const {
    fetchActions: { fetchEntity },
  } = useBusinessContext();

  const { handleEdit } = useAssignmentOperations({
    onSuccess: () => {
      setIsEditDialogOpen(false);
      if (id) {
        fetchAssignment(id);
      }
    }
  });

  useEffect(() => {
    if (id) {
      fetchAssignment(id);
    }
  }, [id]);

  useEffect(() => {
    if (!assignment) return;
    updateSummary(assignment);
  }, [assignment]);

  const updateSummary = async (assignment: IAssignment) => {
    if (assignment && assignment.categoryData) {
      // summary Data
      const categoryData = assignment.categoryData;
      const asPresented = categoryData.reduce((acc, cd) => acc + cd.presentValue, 0);
      const asAnalyzed = categoryData.reduce((acc, cd) => acc + cd.asAnalyzedValue, 0);
      const savings = asPresented - asAnalyzed;
      const percentSaved = asPresented > 0 ? (savings / asPresented) * 100 : 0;

      const summary: AssignmentSummary = {
        budgetary: asPresented,
        asPresented: asPresented,
        undisputed: asAnalyzed,
        savings: savings,
        percentSaved: percentSaved,
        table: categoryData.sort((a, b) => a.category.name.localeCompare(b.category.name))
      };
      console.log('Summary:', summary);
      setAssignmentSummary(summary);
    }
  };

  const fetchAssignment = async (assignmentId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchEntity<IAssignment>(`/v1/assignments/${assignmentId}`);

      if (response.data) {
        setAssignment(response.data);
        console.log('Assignment fetched successfully:', response.data);
      } else if (response.error) {
        const is404Error = response.error.includes('404') ||
          response.error.toLowerCase().includes('not found') ||
          response.error.toLowerCase().includes('does not exist');

        setError(response.error);

        // Only show toast for non-404 errors since 404 is handled by the UI
        if (!is404Error) {
          addToast({
            type: "error",
            title: "Failed to load assignment",
            description: response.error
          });
        }
      }
    } catch {
      const errorMessage = "Failed to load assignment details";
      setError(errorMessage);
      addToast({
        type: "error",
        title: "Error",
        description: errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/assignments');
  };


  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'close':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <CheckCircle2 className="h-3 w-3" />;
      case 'pending':
        return <Clock className="h-3 w-3" />;
      case 'close':
        return <Shield className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  return loading ? (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingSpinner size="lg" />
    </div>
  ) : error || !assignment ? (
    <AssignmentErrorState
      error={error}
      assignmentId={id}
      onRetry={() => {
        if (id) {
          fetchAssignment(id);
        }
      }}
      showPageLayout={true}
    />
  ) : (
    <div className="space-y-8">
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Edit Assignment</DialogTitle>
          </DialogHeader>
          <AssignmentForm
            initialData={assignment || undefined}
            onCancel={() => setIsEditDialogOpen(false)}
            onSubmit={handleEdit}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isROMDialogOpen} onOpenChange={setIsROMDialogOpen}>
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Edit ROM</DialogTitle>
          </DialogHeader>
          <ROMForm
            mode={assignment.rom ? "edit" : "create"}
            initialData={assignment.rom}
            assignmentId={assignment.id}
            onCancel={() => setIsROMDialogOpen(false)}
            fetchAssignment={() => { if (id) fetchAssignment(id) }}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isDocumentsDialogOpen} onOpenChange={setIsDocumentsDialogOpen}>
        <DialogContent className="w-[50vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle>Documents</DialogTitle>
          </DialogHeader>
          <DocumentsView assignmentId={assignment.id} />

        </DialogContent>
      </Dialog>

      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 pb-8 border-b border-border/50">
        <div className="flex gap-4 items-center">
          <Button onClick={handleBack} variant="outline" size="lg" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div className='flex flex-col gap-2'>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight text-foreground">
                {assignment.assignmentName || 'Assignment Details'}
              </h1>
              <Badge
                variant={getStatusBadgeVariant(assignment.status)}
                className="flex items-center gap-1.5 px-3 py-1 text-sm font-medium"
              >
                {getStatusIcon(assignment.status)}
                {assignment.status.toUpperCase()}
              </Badge>
            </div>
            <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
              <span className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4" />
                Created On: {assignment.createdAt ? format(new Date(assignment.createdAt), 'MMM dd, yyyy') : 'N/A'}
              </span>
            </div>
          </div>
        </div>
        <div className='flex gap-4 flex-col items-center'>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsEditDialogOpen(true)}
              size="lg"
              className="gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit Assignment
            </Button>
            <Button
              onClick={() => setIsROMDialogOpen(true)}
              size="lg"
              className="gap-2"
            >
              <HammerIcon className="h-4 w-4" />
              Edit ROM
            </Button>
            <Button
              onClick={() => setIsDocumentsDialogOpen(true)}
              size="lg"
              className="gap-2"
            >
              <FileText className="h-4 w-4" />
              Documents
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-l-4 border-l-blue-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Assignment Name</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {assignment.assignmentName || 'N/A'}
                  </p>
                </div>
                <Briefcase className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-red-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Created By</p>
                  <p className="text-lg font-semibold text-foreground mt-1 font-mono">
                    {assignment.createdBy?.name || 'N/A'}
                  </p>
                </div>
                <User className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-purple-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {format(new Date(assignment.updatedAt), 'MMM dd, yyyy HH:mm') || 'N/A'}
                  </p>
                </div>
                <CalendarSync className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-orange-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Date of Loss</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {assignment.dateOfLoss ? format(new Date(assignment.dateOfLoss), 'MMM dd, yyyy') : 'N/A'}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          {/* Next row */}
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Budgetary</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${assignmentSummary?.budgetary.toFixed(2) || '---'}
                  </p>
                </div>
                <Currency className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">As Presented</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${assignmentSummary?.asPresented.toFixed(2) || '---'}
                  </p>
                </div>
                <BanknoteArrowUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Undisputed</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${assignmentSummary?.undisputed.toFixed(2) || '---'}
                  </p>
                </div>
                <BanknoteArrowDown className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Savings</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${assignmentSummary?.savings.toFixed(2) || '---'}
                  </p>
                </div>
                <HandCoins className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-green-500 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">% Saved</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {assignmentSummary?.percentSaved ? `${assignmentSummary.percentSaved.toFixed(2)}%` : '---'}
                  </p>
                </div>
                <Percent className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <table className="w-full min-w-[600px] rounded-lg border shadow-sm hover:shadow-md transition-shadow">
            <thead>
              <tr className='border-b bg-muted/50'>
                <th className="text-sm font-semibold text-muted-foreground">
                  Values / Category
                </th>
                {
                  assignmentSummary?.table.map((category) => (
                    <th key={category.categoryId} className="px-2 py-3 text-center font-medium text-muted-foreground">
                      {category.category.name}
                    </th>
                  ))
                }
              </tr>
            </thead>
            <tbody>
              <tr className='border-b hover:bg-muted/50'>
                <td className='px-4 py-3'>AP</td>
                {
                  assignmentSummary?.table.map((cd) => (
                    <td key={cd.categoryId} className="px-4 py-3 text-center">
                      {cd.presentValue}
                    </td>
                  ))
                }
              </tr>
              <tr className='border-b hover:bg-muted/50'>
                <td className='px-4 py-3'>AA</td>
                {
                  assignmentSummary?.table.map((cd) => (
                    <td key={cd.categoryId} className="px-4 py-3 text-center">
                      {cd.asAnalyzedValue}
                    </td>
                  ))
                }
              </tr>
              <tr className='border-b hover:bg-muted/50'>
                <td className='px-4 py-3'>Savings</td>
                {
                  assignmentSummary?.table.map((cd) => (
                    <td key={cd.categoryId} className="px-4 py-3 text-center">
                      {cd.presentValue - cd.asAnalyzedValue}
                    </td>
                  ))
                }
              </tr>
              <tr className='border-b hover:bg-muted/50'>
                <td className='px-4 py-3'>%</td>
                {
                  assignmentSummary?.table.map((cd) => (
                    <td key={cd.categoryId} className="px-4 py-3 text-center">
                      {cd.presentValue > 0 ? ((cd.presentValue - cd.asAnalyzedValue) / cd.presentValue * 100).toFixed(2) : '---'}
                    </td>
                  ))
                }
              </tr>

            </tbody>
          </table>
        </div>

        <div className="columns-1 lg:columns-2 gap-8 space-y-8">
          {/* Team Members */}
          <Card className="shadow-sm hover:shadow-md transition-shadow break-inside-avoid mb-8">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 bg-accent/30 rounded-lg border border-border/50 min-h-fit">
                <div className="flex items-start justify-between mb-3">
                  <p className="text-sm font-semibold text-foreground">Assignment Lead</p>
                  <Badge variant="outline" className="text-xs flex-shrink-0">
                    <UserIcon className="w-3 h-3" />
                  </Badge>
                </div>
                <div className="space-y-2">
                  <p className="font-medium text-foreground break-words">{assignment.assignmentLead?.name || 'Not assigned'}</p>
                  {assignment.assignmentLead?.email && (
                    <p className="text-sm text-muted-foreground flex items-start gap-2">
                      <Mail className="h-4 w-4 flex-shrink-0 mt-0.5" />
                      <span className="break-all">{assignment.assignmentLead.email}</span>
                    </p>
                  )}
                  {assignment.assignmentLead?.phone && (
                    <p className="text-sm text-muted-foreground flex items-center gap-2">
                      <Phone className="h-4 w-4 flex-shrink-0" />
                      <span className="break-words">{assignment.assignmentLead.phone}</span>
                    </p>
                  )}
                </div>
              </div>

              <div className="p-4 bg-accent/30 rounded-lg border border-border/50 min-h-fit">
                <div className="flex items-start justify-between mb-3">
                  <p className="text-sm font-semibold text-foreground">Adjuster</p>
                  <Badge variant="outline" className="text-xs flex-shrink-0">
                    <HammerIcon className="w-3 h-3" />
                  </Badge>
                </div>
                <div className="space-y-2">
                  <p className="font-medium text-foreground break-words">{assignment.adjuster?.name || 'Not assigned'}</p>
                  <div className="space-y-1">
                    {assignment.adjuster?.email && (
                      <p className="text-sm text-muted-foreground flex items-start gap-2">
                        <Mail className="h-4 w-4 flex-shrink-0 mt-0.5" />
                        <span className="break-all">{assignment.adjuster.email}</span>
                      </p>
                    )}
                    {assignment.adjuster?.phone && (
                      <p className="text-sm text-muted-foreground flex items-center gap-2">
                        <Phone className="h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{assignment.adjuster.phone}</span>
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Loss Location */}
          <Card className="shadow-sm hover:shadow-md transition-shadow break-inside-avoid mb-8">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                  <MapPin className="h-4 w-4 text-red-600 dark:text-red-400" />
                </div>
                Loss Location
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {assignment.lossLocation ? (
                <div className="space-y-6">
                  {/* Embedded Map */}
                  <div className="relative w-full h-80 bg-muted/20 rounded-lg border border-border/30 overflow-hidden shadow-inner">
                    <iframe
                      src={`https://www.google.com/maps?q=${encodeURIComponent(
                        `${assignment.lossLocation.street}, ${assignment.lossLocation.city}, ${assignment.lossLocation.state} ${assignment.lossLocation.postalCode}`
                      )}&output=embed`}
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      className="rounded-lg"
                      title="Loss Location Map"
                    />
                  </div>

                  {/* Address Information Header */}
                  <div className="flex justify-between text-left items-start gap-4 p-4 rounded-lg border">
                    <div className="min-w-0">
                      <div className="space-y-1">
                        <p className="font-semibold text-foreground text-lg leading-tight break-words">
                          {assignment.lossLocation.street}
                        </p>
                        <p className="text-muted-foreground break-words">
                          {assignment.lossLocation.city}, {assignment.lossLocation.state} {assignment.lossLocation.postalCode}
                        </p>
                        {assignment.lossLocation.country && (
                          <p className="text-sm text-muted-foreground font-medium break-words">
                            {assignment.lossLocation.country}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col justify-right gap-2 flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        className="whitespace-nowrap"
                        onClick={() => {
                          const address = `${assignment.lossLocation?.street}, ${assignment.lossLocation?.city}, ${assignment.lossLocation?.state} ${assignment.lossLocation?.postalCode}`;
                          window.open(`https://maps.google.com/?q=${encodeURIComponent(address)}`, '_blank');
                        }}
                      >
                        <MapPin className="h-4 w-4 mr-2" />
                        Open in Maps
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="whitespace-nowrap"
                        onClick={() => {
                          const address = `${assignment.lossLocation?.street}, ${assignment.lossLocation?.city}, ${assignment.lossLocation?.state} ${assignment.lossLocation?.postalCode}`;
                          navigator.clipboard.writeText(address);
                        }}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center p-8 bg-muted/20 rounded-lg border border-dashed border-border min-h-[200px]">
                  <div className="text-center">
                    <MapPin className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                    <p className="text-sm text-muted-foreground font-medium">No location information available</p>
                    <p className="text-xs text-muted-foreground mt-1">Location details will appear here when available</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contacts */}
          <Card className="shadow-sm hover:shadow-md transition-shadow break-inside-avoid mb-8">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                Contacts
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 bg-accent/30 rounded-lg border border-border/50 min-h-fit">
                <div className="flex items-start justify-between mb-3">
                  <p className="text-sm font-semibold text-foreground">Primary</p>
                  <Badge variant="default" className="text-xs flex-shrink-0">Contact</Badge>
                </div>
                {assignment.primaryContact ? (
                  <div className="space-y-3">
                    <div>
                      <p className="font-medium text-foreground break-words">{assignment.primaryContact.name}</p>
                      <p className="text-sm text-muted-foreground break-words">{assignment.primaryContact.jobTitle}</p>
                    </div>
                    <div className="grid grid-cols-1 gap-2">
                      <p className="text-sm text-muted-foreground flex items-start gap-2">
                        <Mail className="h-4 w-4 flex-shrink-0 mt-0.5" />
                        <span className="break-all">{assignment.primaryContact.email}</span>
                      </p>
                      <p className="text-sm text-muted-foreground flex items-center gap-2">
                        <Phone className="h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{assignment.primaryContact.phone}</span>
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No primary contact assigned</p>
                )}
              </div>

              {assignment.secondaryContact && (
                <div className="p-4 bg-accent/30 rounded-lg border border-border/50 min-h-fit">
                  <div className="flex items-start justify-between mb-3">
                    <p className="text-sm font-semibold text-foreground">Secondary Contact</p>
                    <Badge variant="secondary" className="text-xs flex-shrink-0">Secondary</Badge>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <p className="font-medium text-foreground break-words">{assignment.secondaryContact.name}</p>
                      <p className="text-sm text-muted-foreground break-words">{assignment.secondaryContact.jobTitle}</p>
                    </div>
                    <div className="grid grid-cols-1 gap-2">
                      <p className="text-sm text-muted-foreground flex items-start gap-2">
                        <Mail className="h-4 w-4 flex-shrink-0 mt-0.5" />
                        <span className="break-all">{assignment.secondaryContact.email}</span>
                      </p>
                      <p className="text-sm text-muted-foreground flex items-center gap-2">
                        <Phone className="h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{assignment.secondaryContact.phone}</span>
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Carriers */}
          <Card className="shadow-sm hover:shadow-md transition-shadow break-inside-avoid mb-8">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg">
                  <Building2 className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                Carriers
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assignment.carriers && assignment.carriers.length > 0 ? (
                <div className="p-4 bg-accent/30 rounded-lg border border-border/50 overflow-hidden">
                  <div className="grid grid-cols-3 gap-4 pb-3 mb-4 border-b border-border/50">
                    <div className="flex items-center gap-2 text-sm font-semibold text-muted-foreground">
                      <Building2 className="h-4 w-4" />
                      Name
                    </div>
                    <div className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email
                    </div>
                    <div className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone
                    </div>
                  </div>
                  <div className="space-y-3">
                    {assignment.carriers.map((carrier, index) => (
                      <div
                        key={carrier.id}
                        className={`grid grid-cols-3 gap-4 p-3 rounded border transition-colors hover:bg-accent/20 ${index % 2 === 0 ? 'bg-background' : 'bg-accent/10'
                          } border-border/30`}
                      >
                        <div className="font-medium text-foreground break-words overflow-hidden">
                          {carrier.name}
                        </div>
                        <div className="text-sm text-muted-foreground break-words overflow-hidden">
                          {carrier.email || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground break-words overflow-hidden">
                          {carrier.phone || 'N/A'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="p-4 bg-muted/30 rounded-lg border border-dashed border-border">
                  <p className="text-sm text-muted-foreground text-center">No carriers assigned</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Assignment Description */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-teal-100 dark:bg-teal-900/20 rounded-lg">
                  <FileText className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                </div>
                Assignment Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-accent/30 rounded-lg border border-border/50">
                {assignment.assignmentDescription ? (
                  <div className="text-left">
                    <p className="text-sm leading-relaxed whitespace-pre-wrap text-foreground text-left">
                      {assignment.assignmentDescription}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No assignment description provided
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Claim Details */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="p-2 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg">
                  <FileText className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
                </div>
                Claim Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-accent/30 rounded-lg border border-border/50">
                {assignment.claimDetail ? (
                  <div className="prose prose-sm max-w-none text-left">
                    <p className="text-sm leading-relaxed whitespace-pre-wrap text-foreground text-left">
                      {assignment.claimDetail}
                    </p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No claim details provided
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div >

        <div className="space-y-8">
          {/* Comments Section */}
          <CommentsSection assignmentId={assignment.id} />


          {/* Category Tabs */}
          <CategoryTabs
            assignmentId={assignment.id}
            categoryData={assignment.categoryData}
            onUpdate={updateSummary}
          />

          {/* Role Tabs */}
          {assignment.rom && (
            <RoleTabs romId={assignment.rom.id} onUpdate={() => updateSummary(assignment)} />
          )}
        </div >
      </div >
    </div >
  );
}
