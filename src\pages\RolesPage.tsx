import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>er, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Users, Plus } from 'lucide-react';
import { useBusinessContext } from '@/context/BusinessContext';
import type { IRole } from '@/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RoleForm } from '@/components/pages/roles/RoleForm';

export function Roles() {
  const [isRoleFormDialogOpen, setIsRoleFormDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<IRole | null>(null);
  const {
    entities: { roles },
    loading,
    contextError,
    fetchActions: { fetchRoles },
  } = useBusinessContext();

  useEffect(() => {
    fetchRoles();
  }, []);

  const columns: Column<IRole>[] = [
    {
      key: 'name',
      header: 'Role Name',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
            <Users className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium">{value}</div>
          </div>
        </div>
      )
    },
    {
      key: 'value',
      header: 'Value',
      sortable: true,
      render: (value) => (
        <div className="font-medium">
          ${value.toLocaleString()}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingRole(row);
              setIsRoleFormDialogOpen(true);
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement delete functionality
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Roles"
        description="Manage worker roles and their associated values"
      >
        <Button onClick={() => {
          setEditingRole(null);
          setIsRoleFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Role
        </Button>
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}
        <DataTable
          data={roles}
          columns={columns}
          searchPlaceholder="Search roles..."
          onRowClick={(role) => console.log('View role:', role)}
          loading={loading.roles}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isRoleFormDialogOpen} onOpenChange={setIsRoleFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingRole ? 'Edit Role' : 'Add New Role'}
            </DialogTitle>
          </DialogHeader>
          <RoleForm
            mode={editingRole ? 'edit' : 'create'}
            initialData={editingRole || undefined}
            onCancel={() => setIsRoleFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
