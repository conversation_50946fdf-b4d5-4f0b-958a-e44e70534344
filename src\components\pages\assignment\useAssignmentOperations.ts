import { useCallback } from 'react';
import type { z } from 'zod';
import { useUserContext } from '@/context/UserContext';
import { useBusinessContext } from '@/context/BusinessContext';
import { useToast } from '@/components/custom-ui/toast';
import type {
  IAssignment,
  ILocation,
  IContact,
  ILocationCreatePayload,
  ILocationUpdatePayload,
  IContactCreatePayload,
  IContactUpdatePayload,
  IAssignmentCreatePayload,
  IAssignmentUpdatePayload,
} from '@/types';
import type { EntityResponse } from "@/context/types/BusinessTypes"
import { assignmentFormSchema } from './AssignmentForm';

interface UseAssignmentOperationsOptions {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function useAssignmentOperations(options: UseAssignmentOperationsOptions = {}) {
  const { user } = useUserContext();
  const { entityActions: { createEntity, updateEntity, deleteEntity } } = useBusinessContext();
  const { addToast } = useToast();

  const { onSuccess, onError } = options;

  const handleSave = useCallback(async (formData: z.infer<typeof assignmentFormSchema>) => {
    if (!user) {
      const errorMsg = 'User is not authenticated';
      console.error(errorMsg);
      addToast({
        type: "error",
        title: "Authentication required",
        description: "You must be logged in to create assignments."
      });
      onError?.(errorMsg);
      return;
    }

    try {
      const lossLocationResponse = await createEntity<ILocationCreatePayload, ILocation>("/v1/locations", formData.lossLocation);
      if (lossLocationResponse.error || !lossLocationResponse.data) {
        addToast({
          type: "error",
          title: "Location creation failed",
          description: lossLocationResponse.error || "Failed to create loss location"
        });
        onError?.(lossLocationResponse.error || "Failed to create loss location");
        return;
      }
      const lossLocationId = lossLocationResponse.data.id;

      const primaryContactResponse = await createEntity<IContactCreatePayload, IContact>("/v1/contacts", formData.primaryContact);
      if (primaryContactResponse.error || !primaryContactResponse.data) {
        addToast({
          type: "error",
          title: "Contact creation failed",
          description: primaryContactResponse.error || "Failed to create primary contact"
        });
        onError?.(primaryContactResponse.error || "Failed to create primary contact");
        return;
      }
      const primaryContactId = primaryContactResponse.data.id;

      let secondaryContactId: number | null = null;
      if (formData.secondaryContact) {
        const secondaryContactResponse = await createEntity<IContactCreatePayload, IContact>("/v1/contacts", formData.secondaryContact);
        if (secondaryContactResponse.error || !secondaryContactResponse.data) {
          addToast({
            type: "error",
            title: "Contact creation failed",
            description: secondaryContactResponse.error || "Failed to create secondary contact"
          });
          onError?.(secondaryContactResponse.error || "Failed to create secondary contact");
          return;
        }
        secondaryContactId = secondaryContactResponse.data.id;
      }

      const assignmentData: IAssignmentCreatePayload = {
        status: formData.status,
        assignmentLeadId: formData.assignmentLeadId,
        adjusterId: formData.adjusterId,
        carriers: formData.carriers,
        claimNumber: formData.claimNumber,
        insuredName: formData.insuredName,
        assignmentName: formData.assignmentName,
        dateOfLoss: formData.dateOfLoss,
        claimDetail: formData.claimDetail,
        assignmentDescription: formData.assignmentDescription,
        lossLocationId,
        primaryContactId,
        secondaryContactId
      };

      const result = await createEntity<IAssignmentCreatePayload, IAssignment>("/v1/assignments", assignmentData);
      if (result.error === null && result.data != null) {
        console.log('Assignment created successfully');
        addToast({
          type: "success",
          title: "Assignment created",
          description: "The assignment has been created successfully."
        });
        onSuccess?.();
        return result.data;
      } else {
        addToast({
          type: "error",
          title: "Assignment creation failed",
          description: result.error || "Failed to create assignment"
        });
        onError?.(result.error || "Failed to create assignment");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "An unexpected error occurred";
      addToast({
        type: "error",
        title: "Assignment creation failed",
        description: errorMsg
      });
      onError?.(errorMsg);
    }
  }, [user, createEntity, addToast, onSuccess, onError]);

  const handleEdit = useCallback(async (formData: z.infer<typeof assignmentFormSchema>) => {
    if (!user) {
      const errorMsg = 'User is not authenticated';
      console.error(errorMsg);
      addToast({
        type: "error",
        title: "Authentication required",
        description: "You must be logged in to edit assignments."
      });
      onError?.(errorMsg);
      return;
    }

    if (!formData.id) {
      const errorMsg = 'Assignment ID is missing';
      console.error(errorMsg);
      addToast({
        type: "error",
        title: "Edit failed",
        description: "Assignment ID is missing."
      });
      onError?.(errorMsg);
      return;
    }

    try {
      if (!formData.lossLocation.id) {
        const errorMsg = 'Loss location ID is missing';
        console.error(errorMsg);
        addToast({
          type: "error",
          title: "Edit failed",
          description: "Loss location ID is missing."
        });
        onError?.(errorMsg);
        return;
      }

      const lossLocationResponse = await updateEntity<ILocationUpdatePayload, ILocation>(`/v1/locations/${formData.lossLocation.id}`, {
        ...formData.lossLocation,
        id: formData.lossLocation.id
      });

      if (lossLocationResponse.error || !lossLocationResponse.data) {
        addToast({
          type: "error",
          title: "Location update failed",
          description: lossLocationResponse.error || "Failed to update loss location"
        });
        onError?.(lossLocationResponse.error || "Failed to update loss location");
        return;
      }

      if (!formData.primaryContact.id) {
        const errorMsg = 'Primary contact ID is missing';
        console.error(errorMsg);
        addToast({
          type: "error",
          title: "Edit failed",
          description: "Primary contact ID is missing."
        });
        onError?.(errorMsg);
        return;
      }

      const primaryContactResponse = await updateEntity<IContactUpdatePayload, IContact>(`/v1/contacts/${formData.primaryContact.id}`, {
        ...formData.primaryContact,
        id: formData.primaryContact.id
      });

      if (primaryContactResponse.error || !primaryContactResponse.data) {
        addToast({
          type: "error",
          title: "Contact update failed",
          description: primaryContactResponse.error || "Failed to update primary contact"
        });
        onError?.(primaryContactResponse.error || "Failed to update primary contact");
        return;
      }

      let secondaryContactResponse: EntityResponse<IContact> | null = null;
      if (formData.secondaryContact) {
        if (formData.secondaryContact.id) {
          secondaryContactResponse = await updateEntity<IContactUpdatePayload, IContact>(`/v1/contacts/${formData.secondaryContact.id}`, {
            ...formData.secondaryContact,
            id: formData.secondaryContact.id
          });
        } else {
          secondaryContactResponse = await createEntity<IContactCreatePayload, IContact>("/v1/contacts", {
            ...formData.secondaryContact,
          });
        }

        if (secondaryContactResponse.error || !secondaryContactResponse.data) {
          addToast({
            type: "error",
            title: "Contact operation failed",
            description: secondaryContactResponse.error || "Failed to handle secondary contact"
          });
          onError?.(secondaryContactResponse.error || "Failed to handle secondary contact");
          return;
        }
      } else if (formData.secondaryContactId) {
        const deleteResponse = await deleteEntity(`/v1/contacts/${formData.secondaryContactId}`);
        if (deleteResponse.error) {
          addToast({
            type: "error",
            title: "Contact deletion failed",
            description: deleteResponse.error || "Failed to delete secondary contact"
          });
          return;
        }
      }

      const assignmentUpdatePayload: IAssignmentUpdatePayload = {
        status: formData.status,
        assignmentLeadId: formData.assignmentLeadId,
        adjusterId: formData.adjusterId,
        carriers: formData.carriers,
        claimNumber: formData.claimNumber,
        insuredName: formData.insuredName,
        assignmentName: formData.assignmentName,
        dateOfLoss: formData.dateOfLoss,
        lossLocationId: lossLocationResponse.data.id,
        primaryContactId: primaryContactResponse.data.id,
        secondaryContactId: secondaryContactResponse?.data?.id || null,
        claimDetail: formData.claimDetail,
        assignmentDescription: formData.assignmentDescription,
      };

      const result = await updateEntity<IAssignmentUpdatePayload, IAssignment>(`/v1/assignments/${formData.id}`, assignmentUpdatePayload);

      if (result.error === null && result.data != null) {
        addToast({
          type: "success",
          title: "Assignment updated",
          description: "The assignment has been updated successfully."
        });
        onSuccess?.();
        return result.data;
      } else {
        console.error('Failed to update assignment:', result.error);
        addToast({
          type: "error",
          title: "Assignment update failed",
          description: result.error || "Failed to update assignment. Please try again."
        });
        onError?.(result.error || "Failed to update assignment");
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "An unexpected error occurred";
      console.error('Error updating assignment:', error);
      addToast({
        type: "error",
        title: "Update failed",
        description: errorMsg
      });
      onError?.(errorMsg);
    }
  }, [user, updateEntity, createEntity, addToast, onSuccess, onError]);

  const handleDelete = useCallback(async (assignment: IAssignment) => {
    try {
      const result = await deleteEntity(`/v1/assignments/${assignment.id}`);
      if (result.error === null) {
        console.log('Assignment deleted successfully');
        addToast({
          type: "success",
          title: "Assignment deleted",
          description: "The assignment has been deleted successfully."
        });
        onSuccess?.();
        return true;
      } else {
        console.error('Failed to delete assignment:', result.error);
        addToast({
          type: "error",
          title: "Assignment deletion failed",
          description: result.error || "Failed to delete assignment."
        });
        onError?.(result.error || "Failed to delete assignment");
        return false;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "An unexpected error occurred";
      console.error('Error deleting assignment:', error);
      addToast({
        type: "error",
        title: "Delete failed",
        description: errorMsg
      });
      onError?.(errorMsg);
      return false;
    }
  }, [deleteEntity, addToast, onSuccess, onError]);

  return {
    handleSave,
    handleEdit,
    handleDelete
  };
}