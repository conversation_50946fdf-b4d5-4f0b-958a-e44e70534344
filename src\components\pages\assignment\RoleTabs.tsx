import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/custom-ui/loading";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import type {
  IRomRole,
  IRomRoleCreatePayload,
  IRomRoleUpdatePayload,
} from "@/types";
import { Save, Users, Plus, Trash2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface RoleTabsProps {
  romId: number;
  onUpdate?: () => void;
}

export function RoleTabs({ romId, onUpdate }: RoleTabsProps) {
  const [roleAssignments, setRoleAssignments] = useState<IRomRole[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [newRoleId, setNewRoleId] = useState<string>("");
  const [newQuantity, setNewQuantity] = useState<number>(1);

  const {
    entities: { roles },
    fetchActions: { fetchRoles, fetchEntity },
    entityActions: { createEntity, updateEntity, deleteEntity },
  } = useBusinessContext();
  const { addToast } = useToast();

  useEffect(() => {
    fetchRoles();
    fetchRoleAssignments();
  }, [romId]);

  const fetchRoleAssignments = async () => {
    setLoading(true);
    try {
      const response = await fetchEntity<{results: IRomRole[]}>(
        `/v1/rom-roles/roms/${romId}`
      );
      if (response.data) {
        console.log("Role Assignments:", response.data.results);
        setRoleAssignments(response.data.results);
      } else if (response.error) {
        addToast({
          type: "error",
          title: "Failed to load role assignments",
          description: response.error,
        });
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to load role assignments",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (roleId: number, quantity: number) => {
    setRoleAssignments((prev) =>
      prev.map((assignment) =>
        assignment.roleId === roleId ? { ...assignment, quantity } : assignment
      )
    );
  };

  const handleSaveRole = async (roleId: number, quantity: number) => {
    setSaving(true);
    try {
      const payload: IRomRoleUpdatePayload = {
        romId,
        roleId,
        quantity,
      };

      const response = await updateEntity<IRomRoleUpdatePayload, IRomRole>(
        `/v1/rom-roles/roms/${romId}/roles/${roleId}`,
        payload
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to update role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role updated",
          description: "Role quantity has been updated successfully.",
        });
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to update role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAddRole = async () => {
    if (!newRoleId || newQuantity <= 0) {
      addToast({
        type: "error",
        title: "Invalid input",
        description: "Please select a role and enter a valid quantity.",
      });
      return;
    }

    // Check if role is already assigned
    if (
      roleAssignments.some(
        (assignment) => assignment.roleId === parseInt(newRoleId)
      )
    ) {
      addToast({
        type: "error",
        title: "Role already assigned",
        description: "This role is already assigned to this ROM.",
      });
      return;
    }

    setSaving(true);
    try {
      const payload: IRomRoleCreatePayload = {
        romId,
        roleId: parseInt(newRoleId),
        quantity: newQuantity,
      };

      const response = await createEntity<IRomRoleCreatePayload, IRomRole>(
        `/v1/rom-roles`,
        payload
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to add role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role added",
          description: "Role has been added successfully.",
        });
        setNewRoleId("");
        setNewQuantity(1);
        fetchRoleAssignments();
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to add role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveRole = async (roleId: number) => {
    setSaving(true);
    try {
      const response = await deleteEntity(
        `/v1/rom-roles/roms/${romId}/roles/${roleId}`
      );

      if (response.error) {
        addToast({
          type: "error",
          title: "Failed to remove role",
          description: response.error,
        });
      } else {
        addToast({
          type: "success",
          title: "Role removed",
          description: "Role has been removed successfully.",
        });
        fetchRoleAssignments();
        onUpdate?.();
      }
    } catch (error) {
      addToast({
        type: "error",
        title: "Failed to remove role",
        description: `An unexpected error occurred: ${error}`,
      });
    } finally {
      setSaving(false);
    }
  };

  const availableRoles = roles.filter(
    (role) =>
      !roleAssignments.some((assignment) => assignment.roleId === role.id)
  );

  if (loading) {
    return (
      <Card className="shadow-sm">
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg">
            <Users className="h-5 w-5 text-primary" />
          </div>
          ROM Roles
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Role Assignments */}
        {roleAssignments.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-medium text-sm text-muted-foreground">
              Assigned Roles
            </h3>
            {roleAssignments.map((assignment) => (
              <div
                key={assignment.roleId}
                className="flex items-center gap-4 p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="font-medium">{assignment.role.name}</div>
                  <div className="text-sm text-muted-foreground">
                    Value: ${assignment.role.value.toLocaleString()}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor={`quantity-${assignment.roleId}`}
                    className="text-sm"
                  >
                    Quantity:
                  </Label>
                  <Input
                    id={`quantity-${assignment.roleId}`}
                    type="number"
                    min="1"
                    value={assignment.quantity}
                    onChange={(e) =>
                      handleQuantityChange(
                        assignment.roleId,
                        parseInt(e.target.value) || 1
                      )
                    }
                    className="w-20"
                  />
                  <Button
                    size="sm"
                    onClick={() =>
                      handleSaveRole(assignment.roleId, assignment.quantity)
                    }
                    disabled={saving}
                  >
                    <Save className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleRemoveRole(assignment.roleId)}
                    disabled={saving}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add New Role */}
        {availableRoles.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-medium text-sm text-muted-foreground">
              Add New Role
            </h3>
            <div className="flex items-end gap-4 p-4 border rounded-lg bg-muted/30">
              <div className="flex-1">
                <Label htmlFor="new-role" className="text-sm">
                  Role
                </Label>
                <Select value={newRoleId} onValueChange={setNewRoleId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map((role) => (
                      <SelectItem key={role.id} value={role.id.toString()}>
                        {role.name} (${role.value.toLocaleString()})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="new-quantity" className="text-sm">
                  Quantity
                </Label>
                <Input
                  id="new-quantity"
                  type="number"
                  min="1"
                  value={newQuantity}
                  onChange={(e) =>
                    setNewQuantity(parseInt(e.target.value) || 1)
                  }
                  className="w-20"
                />
              </div>
              <Button onClick={handleAddRole} disabled={saving || !newRoleId}>
                <Plus className="h-4 w-4 mr-2" />
                Add Role
              </Button>
            </div>
          </div>
        )}

        {roleAssignments.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No roles assigned to this ROM yet.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
