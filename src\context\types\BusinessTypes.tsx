import type {
  IAdjuster,
  IAssignment,
  ICarrier,
  ICategory,
  IConsultant,
  IRole,
} from "@/types";

export interface EntityResponse<T> {
  data: T | null;
  error: string | null;
}

export interface ILoading {
  assignments: boolean;
  adjusters: boolean;
  categories: boolean;
  consultants: boolean;
  carriers: boolean;
  roles: boolean;
}

export interface IEntities {
  assignments: IAssignment[];
  adjusters: IAdjuster[];
  categories: ICategory[];
  consultants: IConsultant[];
  carriers: ICarrier[];
  roles: IRole[];
}
export interface BusinessContextType {
  entities: IEntities;
  loading: ILoading;
  contextError: string | null;
  fetchActions: {
    fetchAdjusters: () => Promise<void>;
    fetchAssignments: () => Promise<void>;
    fetchCategories: () => Promise<void>;
    fetchConsultants: () => Promise<void>;
    fetchCarriers: () => Promise<void>;
    fetchRoles: () => Promise<void>;
    fetchEntity: <T>(url: string) => Promise<EntityResponse<T>>;
  };
  entityActions: {
    createEntity: <D extends Record<string, any>, T>(url: string, data: D) => Promise<EntityResponse<T>>;
    updateEntity: <D extends Record<string, any>, T>(url: string, data: D) => Promise<EntityResponse<T>>;
    deleteEntity: (url: string) => Promise<EntityResponse<null>>;
    patchEntity: <D extends Record<string, any>, T>(
      url: string,
      data: D
    ) => Promise<EntityResponse<T>>;
  };
  clearError: () => void;
}
