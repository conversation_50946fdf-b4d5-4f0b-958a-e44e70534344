import { useState, useEffect } from 'react';
import { <PERSON><PERSON>eader, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Phone, Mail, Users } from 'lucide-react';
import { AdjusterForm } from '@/components/pages/adjuster/AdjusterForm';
import { useBusinessContext } from '@/context/BusinessContext';
import type { IAdjuster } from '@/types';

export function Adjusters() {
  const [isAdjusterFormDialogOpen, setIsAdjusterFormDialogOpen] = useState(false);
  const [editingAdjuster, setEditingAdjuster] = useState<IAdjuster | null>(null);

  const {
    entities: { adjusters },
    loading,
    contextError,
    fetchActions: { fetchAdjusters },
  } = useBusinessContext();

  useEffect(() => {
    fetchAdjusters();
  }, [fetchAdjusters]);

  const columns: Column<IAdjuster>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">
              {value.charAt(0).toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1 max-w-[200px]">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            <span className='truncate whitespace-nowrap overflow-hidden block'>{value}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            <span className='truncate whitespace-nowrap overflow-hidden block'>{row.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'carriers',
      header: 'Carriers',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{value?.length || 0}</span>
          <span className="text-sm text-muted-foreground">carriers</span>
        </div>
      )
    },
    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      render: (value) => (
        <span className="text-sm text-muted-foreground">
          {new Date(value).toLocaleDateString()}
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingAdjuster(row);
              setIsAdjusterFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(row.id);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleDelete = (id: number) => {
    // TODO: Implement delete functionality with API
    console.log('Delete adjuster:', id);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Adjusters"
        description="Manage insurance adjusters and their assignments"
      >
        <Button onClick={() => {
          setEditingAdjuster(null);
          setIsAdjusterFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Adjuster
        </Button>
      </PageHeader>

      <PageContent>
        {contextError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            <p>Error: {contextError}</p>
          </div>
        )}

        <DataTable
          data={adjusters}
          columns={columns}
          searchPlaceholder="Search adjusters..."
          onRowClick={(adjuster) => console.log('View adjuster:', adjuster)}
          loading={loading.adjusters}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isAdjusterFormDialogOpen} onOpenChange={setIsAdjusterFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingAdjuster ? 'Edit Adjuster' : 'Add New Adjuster'}
            </DialogTitle>
          </DialogHeader>
          <AdjusterForm
            mode={editingAdjuster ? 'edit' : 'create'}
            initialData={editingAdjuster || undefined}
            onCancel={() => setIsAdjusterFormDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
