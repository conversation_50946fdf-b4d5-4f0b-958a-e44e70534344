export interface ApiResponse<T> {
  data: T | null;
  error: {
    code: string;
    message: string;
    status: number;
    type: string;
  } | null;
}

const BASE_API_URL = import.meta.env.VITE_API_URL;

const BASIC_HEADERS = {
  "Content-Type": "application/json",
};

async function handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
  try {
    const backendResponse = await response.json();

    if (!response.ok) {
      const errorMessage =
        backendResponse.error.message ||
        `HTTP ${response.status}: ${response.statusText}`;
      return {
        data: null,
        error: {
          code: backendResponse?.code || "HTTP_ERROR",
          message: errorMessage,
          status: response.status,
          type: backendResponse?.type || "HTTP_ERROR",
        },
      };
    }
    return backendResponse;
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function getFromApi<T>(
  url: string,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "GET",
      headers,
      credentials: auth ? "include" : "omit",
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function postToApi<T, U = any>(
  url: string,
  data: U,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "POST",
      headers,
      credentials: auth ? "include" : "omit",
      body: JSON.stringify(data),
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function postFileToApi<T>(
  url: string,
  file: File,
  extraData?: Record<string, any>,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;

  const formData = new FormData();
  formData.append("file", file);

  if (extraData) {
    Object.entries(extraData).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }

  try {
    const response = await fetch(fullUrl, {
      method: "POST",
      credentials: auth ? "include" : "omit",
      body: formData,
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function putToApi<T, U = any>(
  url: string,
  data: U,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "PUT",
      headers,
      credentials: auth ? "include" : "omit",
      body: JSON.stringify(data),
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function patchToApi<T, U = any>(
  url: string,
  data: U,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "PATCH",
      headers,
      credentials: auth ? "include" : "omit",
      body: JSON.stringify(data),
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function deleteFromApi<T>(
  url: string,
  auth: boolean = true
): Promise<ApiResponse<T>> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "DELETE",
      headers,
      credentials: auth ? "include" : "omit",
    });

    return handleResponse<T>(response);
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}

export async function downloadFromApi(
  url: string,
  auth: boolean = true
): Promise<{ data: Blob | null; error: { code: string; message: string; status: number; type: string } | null }> {
  const fullUrl = BASE_API_URL + url;
  const headers: HeadersInit = { ...BASIC_HEADERS };

  try {
    const response = await fetch(fullUrl, {
      method: "GET",
      headers,
      credentials: auth ? "include" : "omit",
    });

    if (!response.ok) {
      return {
        data: null,
        error: {
          code: "HTTP_ERROR",
          message: `Request failed with status ${response.status}`,
          status: response.status,
          type: "HTTP_ERROR",
        },
      };
    }

    const blob = await response.blob();
    return { data: blob, error: null };
  } catch (error) {
    return {
      data: null,
      error: {
        code: "NETWORK_ERROR",
        message:
          error instanceof Error ? error.message : "Network error occurred",
        status: 0,
        type: "NETWORK_ERROR",
      },
    };
  }
}
