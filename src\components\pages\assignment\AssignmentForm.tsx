import { useEffect, use<PERSON><PERSON><PERSON>, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import {
  formatDateForDisplay
} from "@/lib/utils";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/custom-ui/multiselect";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/custom-ui/textarea";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, Di<PERSON>Title } from "@/components/ui/dialog";
import { LoadingSpinner } from "@/components/custom-ui/loading";
import { AdjusterForm } from "@/components/pages/adjuster/AdjusterForm";
import { CategoryForm } from "@/components/pages/categories/CategoryForm";
import { CarrierForm } from "@/components/pages/carrier/CarrierForm";
import { ConsultantForm } from "@/components/pages/consultant/ConsultantForm";
import { useUserContext } from "@/context/UserContext";
import { useBusinessContext } from "@/context/BusinessContext";
import type { IAssignment, ICarrier } from "@/types";

export const locationFormSchema = z.object({
  id: z.number().optional(),
  street: z.string().min(1, "Street is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  country: z.string().min(1, "Country is required"),
})

export const contactFormSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  jobTitle: z.string().min(1, "Job title is required"),
})

export const assignmentFormSchema = z.object({
  id: z.number().optional(),

  status: z.enum(["active", "close", "pending"]),
  assignmentLeadId: z.number(),

  adjusterId: z.number(),
  carriers: z.array(z.number()),
  claimNumber: z.string().min(1, "Claim number is required"),
  insuredName: z.string().min(1, "Insured name is required"),
  assignmentName: z.string().min(1, "Assignment name is required"),
  dateOfLoss: z.string()
    .min(1, "Date of loss is required")
    .refine((val) => {
      try {
        const date = new Date(val);
        return !isNaN(date.getTime()) && date <= new Date();
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid date that is not in the future"
    }),
  lossLocationId: z.number(),
  primaryContactId: z.number(),
  secondaryContactId: z.number().optional(),
  createdById: z.number(),
  claimDetail: z.string(),
  assignmentDescription: z.string(),

  lossLocation: locationFormSchema,
  primaryContact: contactFormSchema,
  secondaryContact: contactFormSchema.optional(),
});

interface AssignmentFormProps {
  initialData?: IAssignment;
  onCancel: () => void;
  onSubmit: (data: z.infer<typeof assignmentFormSchema>) => void;
}

export function AssignmentForm({ initialData, onCancel, onSubmit }: AssignmentFormProps) {
  const [showSecondaryContact, setShowSecondaryContact] = useState(!!initialData?.secondaryContact);
  const [isModalOpen, setIsModalOpen] = useState<"" | "carrier" | "adjuster" | "category" | "consultant">("")
  const { user } = useUserContext();
  const {
    loading,
    entities: {
      adjusters,
      consultants,
      carriers
    },
    fetchActions: {
      fetchAdjusters,
      fetchCategories,
      fetchConsultants,
      fetchCarriers
    },
  } = useBusinessContext();

  useEffect(() => {
    fetchAdjusters();
    fetchCategories();
    fetchConsultants();
    fetchCarriers()
  }, []);

  const form = useForm<z.infer<typeof assignmentFormSchema>>({
    resolver: zodResolver(assignmentFormSchema),
    defaultValues: {
      id: initialData?.id,
      adjusterId: initialData?.adjusterId,
      assignmentLeadId: initialData?.assignmentLeadId,
      lossLocationId: initialData?.lossLocationId || 0,
      createdById: initialData?.createdById || 0,
      primaryContactId: initialData?.primaryContactId || 0,
      secondaryContactId: initialData?.secondaryContactId || 0,
      carriers: initialData?.carriers?.map((carrier: ICarrier) => carrier.id) || [],
      status: initialData?.status as "active" | "close" | "pending" || "active",
      claimNumber: initialData?.claimNumber || "",
      insuredName: initialData?.insuredName || "",
      assignmentName: initialData?.assignmentName || "",
      dateOfLoss: initialData?.dateOfLoss || "",
      claimDetail: initialData?.claimDetail || "",
      assignmentDescription: initialData?.assignmentDescription || "",
      lossLocation: {
        id: initialData?.lossLocation?.id,
        street: initialData?.lossLocation?.street || "",
        city: initialData?.lossLocation?.city || "",
        state: initialData?.lossLocation?.state || "",
        postalCode: initialData?.lossLocation?.postalCode || "",
        country: initialData?.lossLocation?.country || "",
      },
      primaryContact: {
        id: initialData?.primaryContact?.id || 0,
        name: initialData?.primaryContact?.name || "",
        email: initialData?.primaryContact?.email || "",
        phone: initialData?.primaryContact?.phone || "",
        jobTitle: initialData?.primaryContact?.jobTitle || "",
      },
      secondaryContact: showSecondaryContact ? {
        id: initialData?.secondaryContact?.id || 0,
        name: initialData?.secondaryContact?.name || "",
        email: initialData?.secondaryContact?.email || "",
        phone: initialData?.secondaryContact?.phone || "",
        jobTitle: initialData?.secondaryContact?.jobTitle || "",
      } : undefined,
    },
  });

  useEffect(() => {
    const subscription = form.watch((values) => {
      const claimNumber = values.claimNumber || "";
      const insuredName = values.insuredName || "";

      const combined = insuredName || claimNumber ? `${insuredName} - ${claimNumber}` : "";

      if (form.getValues("assignmentName") !== combined) {
        form.setValue("assignmentName", combined);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  useEffect(() => {
    if (!showSecondaryContact) {
      form.setValue("secondaryContact", undefined);
    }
  }, [showSecondaryContact]);

  const selectedAdjusterId = form.watch("adjusterId");
  const sortedCarriers = useMemo(() => {
    if (!selectedAdjusterId) return carriers;
    let sorted = [...carriers].sort((a, b) => {
      const aMatch = a.adjusterId === selectedAdjusterId ? 0 : 1;
      const bMatch = b.adjusterId === selectedAdjusterId ? 0 : 1;
      return aMatch - bMatch;
    });
    return sorted;
  }, [carriers, selectedAdjusterId]);

  const handleSubmit = (data: z.infer<typeof assignmentFormSchema>) => {
    onSubmit(data);
  };

  return (
    <>
      {
        isModalOpen &&
        <Dialog open={isModalOpen === "adjuster" || isModalOpen === "category" || isModalOpen === "carrier" || isModalOpen === "consultant"} onOpenChange={() => setIsModalOpen("")}>
          <DialogContent className="w-[40vw] max-h-[90vh] overflow-y-auto" aria-describedby={undefined}>
            <DialogHeader>
              <DialogTitle>Add {isModalOpen}</DialogTitle>
            </DialogHeader>
            {
              isModalOpen === "adjuster" ? (
                <AdjusterForm
                  initialData={undefined}
                  onCancel={() => setIsModalOpen("")}
                />
              ) : isModalOpen === "category" ? (
                <CategoryForm
                  initialData={undefined}
                  onCancel={() => setIsModalOpen("")}
                />
              ) : isModalOpen === "carrier" ? (
                <CarrierForm
                  initialData={undefined}
                  onCancel={() => setIsModalOpen("")}
                />
              ) : isModalOpen === "consultant" ? (
                <ConsultantForm
                  initialData={undefined}
                  onCancel={() => setIsModalOpen("")}
                />
              ) : null

            }
          </DialogContent>
        </Dialog>
      }
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <Accordion type="multiple" defaultValue={["system-information", "assignment-information", "location", "contacts"]}>
            {/* 1. Policy Information */}
            <AccordionItem value="system-information">
              <AccordionTrigger>System Information</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={undefined}
                    name="createdOn"
                    render={() => (
                      <FormItem>
                        <FormLabel>Created On</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Created On"
                            disabled
                            value={formatDateForDisplay(initialData?.createdAt || new Date().toISOString())}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={undefined}
                    name="createdBy"
                    render={() => (
                      <FormItem>
                        <FormLabel>Created By</FormLabel>
                        <FormControl>
                          <Input placeholder="Created On" disabled value={initialData?.createdBy?.name || user?.name || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="close">Close</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="assignmentLeadId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assignment Lead</FormLabel>
                        <div className="flex items-center gap-2">
                          {
                            loading.consultants ? (
                              <LoadingSpinner />
                            ) : null
                          }
                          <FormControl>
                            <Select
                              disabled={loading.consultants}
                              value={field.value && field.value !== 0 ? field.value.toString() : ""}
                              onValueChange={(value) => field.onChange(parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select assignment lead" />
                              </SelectTrigger>
                              <SelectContent>
                                {consultants.map((consultant) => (
                                  <SelectItem key={consultant.id} value={consultant.id.toString()}>
                                    {consultant.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <Button type="button" variant={"outline"} onClick={() => setIsModalOpen("consultant")}><Plus className="h-4 w-4" /></Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

              </AccordionContent>
            </AccordionItem>

            {/* 2. Assignment Information */}
            <AccordionItem value="assignment-information">
              <AccordionTrigger>Assignment Information</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="adjusterId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Adjuster</FormLabel>
                        <div className="flex items-center gap-2">
                          {
                            loading.adjusters ? (
                              <LoadingSpinner />
                            ) : null
                          }
                          <FormControl>
                            <Select
                              disabled={loading.adjusters}
                              value={field.value && field.value !== 0 ? field.value.toString() : ""}
                              onValueChange={(value) => field.onChange(parseInt(value))}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select adjuster" />
                              </SelectTrigger>
                              <SelectContent>
                                {adjusters.map((adjuster) => (
                                  <SelectItem key={adjuster.id} value={adjuster.id.toString()}>
                                    {adjuster.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <Button type="button" variant={"outline"} onClick={() => setIsModalOpen("adjuster")}><Plus className="h-4 w-4" /></Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="carriers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Carriers</FormLabel>
                        <div className="flex items-center gap-2">
                          {
                            loading.carriers ? (
                              <LoadingSpinner />
                            ) : null
                          }
                          <FormControl className="flex-1 min-w-0">
                            <MultiSelect
                              options={sortedCarriers.map((carrier) => ({
                                value: carrier.id.toString(),
                                label: carrier.name,
                              }))}
                              selected={field.value.map((v: number) => v.toString())}
                              onChange={(values) => field.onChange(values.map((v) => parseInt(v)))}
                            />
                          </FormControl>
                          <Button className="shrink-0" type="button" variant={"outline"} onClick={() => setIsModalOpen("carrier")}><Plus className="h-4 w-4" /></Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="col-span-3">
                    <div className="col-span-3">
                      <FormField
                        control={form.control}
                        name="assignmentName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assignment Name</FormLabel>
                            <FormControl>
                              <Input
                                readOnly
                                value={field.value || ""}
                                placeholder="Assignment name (auto-generated)"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name="insuredName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Insured Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter insured name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="claimNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Claim Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter claim number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="dateOfLoss"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Loss</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            value={field.value?.slice(0, 10) || ""}
                            onChange={(e) => {
                              const isoString = e.target.value ? new Date(e.target.value).toISOString() : "";
                              field.onChange(isoString);
                            }}
                            max={new Date().toISOString().slice(0, 10)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="col-span-3 space-y-4">
                    <FormField
                      control={form.control}
                      name="assignmentDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Assignment Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter assignment description"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="claimDetail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Claim Detail</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter claim detail"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="location">
              <AccordionTrigger>Loss Location</AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="lossLocation.street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter street" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lossLocation.city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter city" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lossLocation.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter state" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lossLocation.postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter postal code" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lossLocation.country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter country" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="contacts">
              <AccordionTrigger>Contacts</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-6">
                  {/* Primary Contact - Required */}
                  <div>
                    <h4 className="text-lg font-medium mb-4">Insured Primary Contact (Required)</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="primaryContact.name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="primaryContact.email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter email" type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="primaryContact.phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="primaryContact.jobTitle"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Job Title</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter job title" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Secondary Contact Toggle */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="add-secondary-contact"
                      checked={showSecondaryContact}
                      onCheckedChange={(checked) => setShowSecondaryContact(!!checked)}
                    />
                    <label
                      htmlFor="add-secondary-contact"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Add Secondary Contact
                    </label>
                  </div>

                  {/* Secondary Contact - Optional */}
                  {showSecondaryContact && (
                    <div>
                      <h4 className="text-lg font-medium mb-4">Insured Secondary Contact (Optional)</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="secondaryContact.name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="secondaryContact.email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter email" type="email" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="secondaryContact.phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter phone number" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="secondaryContact.jobTitle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Job Title</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter job title" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
            >
              Save
            </Button>
          </div>
        </form>
      </Form >
    </>
  )
}