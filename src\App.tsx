import './App.css'
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/context/ThemeContext";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ToastProvider } from "@/components/custom-ui/toast";
import { Layout } from '@/components/layout/Layout';
import { NotFound } from '@/pages/NotFoundPage';
import { AssignmentPage } from '@/pages/AssignmentPage';
import { AssignmentDetailPage } from '@/components/pages/assignment/DetailPage';
import { Dashboard } from '@/pages/DashboardPage';
import { Carriers } from '@/pages/CarriersPage';
import { Adjusters } from '@/pages/AdjustersPage';
import { Categories } from '@/pages/CategoriesPage';
import { Parameters } from '@/pages/Parameters';
import { ConsultantsPage } from '@/pages/ConsultantsPage';
import { UserProvider } from './context/UserContext';
import { BusinessProvider } from './context/BusinessContext';
import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import { Roles } from "./pages/RolesPage";

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <TooltipProvider>
          <ToastProvider>
            <UserProvider>
              <BusinessProvider>
                <BrowserRouter>
                  <Routes>
                    <Route path="/" element={<Layout />}>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="assignments" element={<AssignmentPage />} />
                      <Route path="assignments/:id" element={<AssignmentDetailPage />} />
                      <Route path="carriers" element={<Carriers />} />
                      <Route path="adjusters" element={<Adjusters />} />
                      <Route path="consultants" element={<ConsultantsPage />} />
                      <Route path="categories" element={<Categories />} />
                      <Route path="roles" element={<Roles />} />
                      <Route path="parameters" element={<Parameters />} />
                      <Route path="*" element={<NotFound />} />
                    </Route>
                  </Routes>
                </BrowserRouter>
              </BusinessProvider>
            </UserProvider>
          </ToastProvider>
        </TooltipProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
