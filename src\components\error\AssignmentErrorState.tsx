import { Alert<PERSON>ir<PERSON>, Home, ArrowLeft, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PageHeader, PageContent } from '@/components/custom-ui/page-header';

interface AssignmentErrorStateProps {
  error?: string | null;
  assignmentId?: string;
  onRetry?: () => void;
  showPageLayout?: boolean;
}

export function AssignmentErrorState({
  error,
  assignmentId,
  onRetry,
  showPageLayout = true
}: AssignmentErrorStateProps) {
  const navigate = useNavigate();

  const is404Error = error?.includes('404') ||
    error?.toLowerCase().includes('not found') ||
    error?.toLowerCase().includes('does not exist');

  const errorTitle = is404Error ? 'Assignment Not Found' : 'Error Loading Assignment';
  const errorMessage = is404Error
    ? `The assignment with ID "${assignmentId}" could not be found. It may have been deleted or the URL may be incorrect.`
    : error || 'An unexpected error occurred while loading the assignment.';

  const content = (
    <Card className="max-w-2xl mx-auto">
      <CardContent className="pt-8 pb-8">
        <div className="text-center space-y-6">
          {/* Error Icon */}
          <div className="flex justify-center">
            <div className={`rounded-full p-4 ${is404Error ? 'bg-orange-100' : 'bg-red-100'}`}>
              <AlertCircle className={`h-12 w-12 ${is404Error ? 'text-orange-600' : 'text-red-600'}`} />
            </div>
          </div>

          {/* Error Message */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground">
              {errorTitle}
            </h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              {errorMessage}
            </p>
            {assignmentId && (
              <p className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded inline-block">
                Assignment ID: {assignmentId}
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
            <Button
              onClick={() => navigate('/assignments')}
              variant="default"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Assignments
            </Button>
            <Button
              onClick={() => navigate('/')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Go to Dashboard
            </Button>
            {onRetry && (
              <Button
                onClick={onRetry}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            )}
          </div>

          {/* Additional Help */}
          <div className="pt-4 border-t border-border">
            <p className="text-sm text-muted-foreground">
              Need help? You can{' '}
              <button
                onClick={() => navigate('/assignments')}
                className="text-primary hover:underline font-medium"
              >
                browse all assignments
              </button>
              {' '}or{' '}
              <button
                onClick={() => navigate('/')}
                className="text-primary hover:underline font-medium"
              >
                return to the dashboard
              </button>
              .
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (!showPageLayout) {
    return content;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={errorTitle}
        description="The assignment you're looking for could not be found."
      />
      <PageContent>
        {content}
      </PageContent>
    </div>
  );
}
