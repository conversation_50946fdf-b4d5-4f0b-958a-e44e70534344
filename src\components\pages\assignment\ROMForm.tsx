import { z } from "zod";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { IROM } from "@/types/domain";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useMemo } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import type { IROMCreatePayload, IROMUpdatePayload } from "@/types";
import { useBusinessContext } from "@/context/BusinessContext";
import { useToast } from "@/components/custom-ui/toast";
import {
  CAUSE_OF_LOSS,
  CEILING_TYPES,
  CONFINED_SPACE,
  CONTAINMENT,
  CONTAMINANT_LEVELS,
  CONTENT_CONCENTRATIONS,
  DEBRIS_REMOVAL,
  ELEVATOR_SYSTEM,
  FACILITY_TYPES,
  FALSE_AIR_SPACE,
  HVAC,
  NUMBER_OF_STORIES,
  WATER_CATEGORY,
  WATER_CLASS,
  ELEVATOR_INCLUDED_IN_SCOPE,
} from "@/pages/types/rom";
import type {
  CauseOfLoss,
  CeilingType,
  ConfinedSpace,
  Containment,
  ContaminantLevel,
  ContentConcentration,
  DebrisRemoval,
  ElevatorSystem,
  FacilityType,
  FalseAirSpace,
  Hvac,
  NumberOfStories,
  WaterCategory,
  WaterClass,
  ElevatorIncludedInScope,
} from "@/pages/types/rom";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export const romFormSchema = z.object({
  id: z.number().optional(),
  assignmentId: z.coerce.number(),
  //1. Project details
  area: z.coerce.number(),
  duration: z.coerce.number(),

  //2. Base Rates & Key Factor
  labourRate: z.coerce.number(),
  materialRate: z.coerce.number(),
  contingency: z.coerce.number(),

  //3. Quotient calculation
  facilityType: z.enum(FACILITY_TYPES.map(type => type.name) as [FacilityType, ...FacilityType[]]),
  ceilingType: z.enum(CEILING_TYPES.map(type => type.name) as [CeilingType, ...CeilingType[]]),
  contaminantLevel: z.enum(CONTAMINANT_LEVELS.map(level => level.name) as [ContaminantLevel, ...ContaminantLevel[]]),
  contentConcentration: z.enum(CONTENT_CONCENTRATIONS.map(concentration => concentration.name) as [ContentConcentration, ...ContentConcentration[]]),
  confinedSpace: z.enum(CONFINED_SPACE.map(space => space.name) as [ConfinedSpace, ...ConfinedSpace[]]),
  containment: z.enum(CONTAINMENT.map(containment => containment.name) as [Containment, ...Containment[]]),
  debrisRemoval: z.enum(DEBRIS_REMOVAL.map(removal => removal.name) as [DebrisRemoval, ...DebrisRemoval[]]),
  elevatorSystem: z.enum(ELEVATOR_SYSTEM.map(system => system.name) as [ElevatorSystem, ...ElevatorSystem[]]),
  falseAirSpace: z.enum(FALSE_AIR_SPACE.map(space => space.name) as [FalseAirSpace, ...FalseAirSpace[]]),
  hvac: z.enum(HVAC.map(hvac => hvac.name) as [Hvac, ...Hvac[]]),
  numberOfStories: z.enum(NUMBER_OF_STORIES.map(stories => stories.name) as [NumberOfStories, ...NumberOfStories[]]),
  causeOfLoss: z.enum(CAUSE_OF_LOSS.map(cause => cause.name) as [CauseOfLoss, ...CauseOfLoss[]]),
  waterCategory: z.enum(WATER_CATEGORY.map(category => category.name) as [WaterCategory, ...WaterCategory[]]),
  waterClass: z.enum(WATER_CLASS.map(waterClass => waterClass.name) as [WaterClass, ...WaterClass[]]),
  elevatorIncludedInScope: z.enum(ELEVATOR_INCLUDED_IN_SCOPE.map(included => included.name) as [ElevatorIncludedInScope, ...ElevatorIncludedInScope[]]),
})

interface ROMFormProps {
  initialData?: IROM;
  assignmentId: number;
  onCancel: () => void;
  mode?: "create" | "edit";
  fetchAssignment: () => void;
}

export function ROMForm({ initialData, assignmentId, onCancel, mode = "create", fetchAssignment }: ROMFormProps) {
  const form = useForm<z.infer<typeof romFormSchema>>({
    resolver: zodResolver(romFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      assignmentId: assignmentId,
      area: initialData?.area || 0,
      duration: initialData?.duration || 0,
      labourRate: initialData?.labourRate || 0,
      materialRate: initialData?.materialRate || 0,
      contingency: initialData?.contingency || 0,
    },
  })

  const {
    entityActions: {
      createEntity,
      updateEntity
    },
  } = useBusinessContext();
  const { addToast } = useToast();

  const {
    area = 0,
    duration = 0,
    labourRate = 0,
    materialRate = 0,
    contingency = 0,

    facilityType = "" as FacilityType,
    ceilingType = "" as CeilingType,
    contaminantLevel = "" as ContaminantLevel,
    contentConcentration = "" as ContentConcentration,
    confinedSpace = "" as ConfinedSpace,
    containment = "" as Containment,
    debrisRemoval = "" as DebrisRemoval,
    elevatorSystem = "" as ElevatorSystem,
    falseAirSpace = "" as FalseAirSpace,
    hvac = "" as Hvac,
    numberOfStories = "" as NumberOfStories,
    causeOfLoss = "" as CauseOfLoss,
    waterCategory = "" as WaterCategory,
    waterClass = "" as WaterClass,
    elevatorIncludedInScope = "" as ElevatorIncludedInScope,
  } = useWatch({ control: form.control });

  const summary = useMemo(() => {
    const H = 8;
    const labourCost = duration * H * labourRate;
    const materialCost = area * materialRate;
    const subTotal = labourCost + materialCost;
    const contingencyCost = subTotal * (contingency / 100);
    const totalROM = subTotal + contingencyCost;

    const lookup = <T extends { name: string; value: number }>(
      arr: readonly T[],
      key: string
    ) => arr.find((o) => o.name === key as any)?.value ?? 0;

    const quotient =
      lookup(FACILITY_TYPES, facilityType) +
      lookup(CEILING_TYPES, ceilingType) +
      lookup(CONTAMINANT_LEVELS, contaminantLevel) +
      lookup(CONTENT_CONCENTRATIONS, contentConcentration) +
      lookup(CONFINED_SPACE, confinedSpace) +
      lookup(CONTAINMENT, containment) +
      lookup(DEBRIS_REMOVAL, debrisRemoval) +
      lookup(ELEVATOR_SYSTEM, elevatorSystem) +
      lookup(FALSE_AIR_SPACE, falseAirSpace) +
      lookup(HVAC, hvac) +
      lookup(NUMBER_OF_STORIES, numberOfStories) +
      lookup(CAUSE_OF_LOSS, causeOfLoss) +
      lookup(WATER_CATEGORY, waterCategory) +
      lookup(WATER_CLASS, waterClass) +
      lookup(ELEVATOR_INCLUDED_IN_SCOPE, elevatorIncludedInScope);
    return { labourCost, materialCost, subTotal, contingencyCost, totalROM, quotient };
  }, [
    area,
    duration,
    labourRate,
    materialRate,
    contingency,
    facilityType,
    ceilingType,
    contaminantLevel,
    contentConcentration,
    confinedSpace,
    containment,
    debrisRemoval,
    elevatorSystem,
    falseAirSpace,
    hvac,
    numberOfStories,
    causeOfLoss,
    waterCategory,
    waterClass,
    elevatorIncludedInScope,
  ]);

  const handleSubmit = (data: z.infer<typeof romFormSchema>) => {
    if (mode === "create") {
      handleCreate(data);
    } else {
      handleEdit(data);
    }
  }

  const handleCreate = async (data: z.infer<typeof romFormSchema>) => {
    const payload: IROMCreatePayload = {
      assignmentId: data.assignmentId,
      area: data.area,
      duration: data.duration,
      labourRate: data.labourRate,
      materialRate: data.materialRate,
      contingency: data.contingency,
    };

    console.log("Creating ROM:", payload);
    const response = await createEntity<IROMCreatePayload, IROM>("/v1/roms", payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "ROM creation failed",
        description: response.error || "Failed to create ROM"
      });
      return;
    }
    addToast({
      type: "success",
      title: "ROM created",
      description: "The ROM has been created successfully."
    })
    onCancel()
    fetchAssignment();
  }

  const handleEdit = async (data: z.infer<typeof romFormSchema>) => {
    if (!initialData || !data.id) return addToast({
      type: "error",
      title: "ROM update failed",
      description: "Failed to update ROM. ROM ID is missing."
    });

    const payload: IROMUpdatePayload = {
      id: data.id,
      assignmentId: data.assignmentId,
      area: data.area,
      duration: data.duration,
      labourRate: data.labourRate,
      materialRate: data.materialRate,
      contingency: data.contingency,
    };

    const response = await updateEntity<IROMUpdatePayload, IROM>(`/v1/roms/${initialData.id}`, payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "ROM update failed",
        description: response.error || "Failed to update ROM"
      });
      return;
    }
    addToast({
      type: "success",
      title: "ROM updated",
      description: "The ROM has been updated successfully."
    })
    onCancel()
    fetchAssignment();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 w-full">
        <Accordion type="multiple" defaultValue={["project-details", "quotient", "summary"]}>
          <AccordionItem value="project-details">
            <AccordionTrigger>Project Details</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Area</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter area" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter duration" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contingency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contingency</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter contingency" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="labourRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Labour Rate</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter labour rate" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="materialRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Material Rate</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter material rate" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="quotient">
            <AccordionTrigger>Quotient Calculation</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 2xl:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="facilityType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Facility Type</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select facility type" />
                          </SelectTrigger>
                          <SelectContent>
                            {FACILITY_TYPES.map((facilityType) => (
                              <SelectItem key={facilityType.name} value={facilityType.name}>
                                {facilityType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="ceilingType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ceiling Type</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select ceiling type" />
                          </SelectTrigger>
                          <SelectContent>
                            {CEILING_TYPES.map((ceilingType) => (
                              <SelectItem key={ceilingType.name} value={ceilingType.name}>
                                {ceilingType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contaminantLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contaminant Level</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select contaminant level" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTAMINANT_LEVELS.map((contaminantLevel) => (
                              <SelectItem key={contaminantLevel.name} value={contaminantLevel.name}>
                                {contaminantLevel.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contentConcentration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Content Concentration</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select content concentration" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTENT_CONCENTRATIONS.map((contentConcentration) => (
                              <SelectItem key={contentConcentration.name} value={contentConcentration.name}>
                                {contentConcentration.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confinedSpace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confined Space</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select confined space" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONFINED_SPACE.map((confinedSpace) => (
                              <SelectItem key={confinedSpace.name} value={confinedSpace.name}>
                                {confinedSpace.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="containment"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Containment</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ?? ""}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select containment" />
                          </SelectTrigger>
                          <SelectContent>
                            {CONTAINMENT.map((containment) => (
                              <SelectItem key={containment.name} value={containment.name}>
                                {containment.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="debrisRemoval"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Debris Removal</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select debris removal" />
                          </SelectTrigger>
                          <SelectContent>
                            {DEBRIS_REMOVAL.map((removal) => (
                              <SelectItem key={removal.name} value={removal.name}>
                                {removal.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="elevatorSystem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Elevator System</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select elevator system" />
                          </SelectTrigger>
                          <SelectContent>
                            {ELEVATOR_SYSTEM.map((system) => (
                              <SelectItem key={system.name} value={system.name}>
                                {system.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="falseAirSpace"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>False Air Space</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select false air space" />
                          </SelectTrigger>
                          <SelectContent>
                            {FALSE_AIR_SPACE.map((space) => (
                              <SelectItem key={space.name} value={space.name}>
                                {space.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="hvac"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>HVAC</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select HVAC" />
                          </SelectTrigger>
                          <SelectContent>
                            {HVAC.map((hvac) => (
                              <SelectItem key={hvac.name} value={hvac.name}>
                                {hvac.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="numberOfStories"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Stories</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select number of stories" />
                          </SelectTrigger>
                          <SelectContent>
                            {NUMBER_OF_STORIES.map((stories) => (
                              <SelectItem key={stories.name} value={stories.name}>
                                {stories.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="causeOfLoss"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cause of Loss</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select cause of loss" />
                          </SelectTrigger>
                          <SelectContent>
                            {CAUSE_OF_LOSS.map((cause) => (
                              <SelectItem key={cause.name} value={cause.name}>
                                {cause.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="waterCategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Water Category</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select water category" />
                          </SelectTrigger>
                          <SelectContent>
                            {WATER_CATEGORY.map((category) => (
                              <SelectItem key={category.name} value={category.name}>
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="waterClass"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Water Class</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select water class" />
                          </SelectTrigger>
                          <SelectContent>
                            {WATER_CLASS.map((waterClass) => (
                              <SelectItem key={waterClass.name} value={waterClass.name}>
                                {waterClass.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="elevatorIncludedInScope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Elevator Included In Scope</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={(value) => field.onChange(value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select elevator included in scope" />
                          </SelectTrigger>
                          <SelectContent>
                            {ELEVATOR_INCLUDED_IN_SCOPE.map((included) => (
                              <SelectItem key={included.name} value={included.name}>
                                {included.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="summary">
            <AccordionTrigger>Summary</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-5 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Quotient</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    {summary.quotient}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Labour Cost</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${summary.labourCost.toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Material Cost</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${summary.materialCost.toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Sub Total</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${summary.subTotal.toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Contingency</p>
                  <p className="text-lg font-semibold text-foreground mt-1">
                    ${summary.contingencyCost.toFixed(2)}
                  </p>
                </div>
              </div>

              <div className="mt-6 border-t border-muted-foreground pt-4 flex justify-end items-center">
                <div className="bg-primary/10 px-5 py-3 rounded-lg flex items-center space-x-3">
                  <span className="text-sm font-medium text-muted-foreground">Total ROM</span>
                  <span className="text-2xl font-bold text-primary">
                    ${summary.totalROM.toFixed(2)}
                  </span>
                </div>
              </div>
            </AccordionContent>

          </AccordionItem>
        </Accordion>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form >
  )
}
