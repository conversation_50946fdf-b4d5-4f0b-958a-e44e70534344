import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUserContext } from "@/context/UserContext";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/custom-ui/toast";
import type { IAdjusterCreatePayload, IAdjusterUpdatePayload } from "@/types/payload/adjuster";
import type { IAdjuster } from "@/types";
import { useBusinessContext } from "@/context/BusinessContext";

export const adjusterFormSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  phone: z.string().min(1, "Phone number is required"),
})

interface AdjusterFormProps {
  initialData?: IAdjuster;
  mode?: "create" | "edit";
  onCancel: () => void;
}
export function AdjusterForm({ initialData, onCancel, mode = "create" }: AdjusterFormProps) {
  const { user } = useUserContext();
  const {
    entityActions: {
      createEntity,
      updateEntity
    },
    fetchActions: {
      fetchAdjusters,
    },
  } = useBusinessContext();
  const { addToast } = useToast();

  const form = useForm<z.infer<typeof adjusterFormSchema>>({
    resolver: zodResolver(adjusterFormSchema),
    defaultValues: {
      id: initialData?.id || 0,
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
    },
  })

  const handleSubmit = async (data: z.infer<typeof adjusterFormSchema>) => {
    if (!user) return addToast({
      type: "error",
      title: "Adjuster creation failed",
      description: "You must be logged in to create an adjuster."
    });

    if (mode === "create") {
      handleCreate(data);
    } else {
      handleEdit(data);
    }
  }

  const handleCreate = async (data: z.infer<typeof adjusterFormSchema>) =>{
    const payload: IAdjusterCreatePayload = {
      name: data.name,
      email: data.email,
      phone: data.phone,
    };
    const response = await createEntity<IAdjusterCreatePayload, IAdjuster>("/v1/adjusters", payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Adjuster creation failed",
        description: response.error || "Failed to create adjuster"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Adjuster created",
      description: "The adjuster has been created successfully."
    })
    console.log("Adjuster created successfully:", response.data);
    fetchAdjusters()
    onCancel();
  }

  const handleEdit = async (data: z.infer<typeof adjusterFormSchema>) => {
    if (!initialData || !data.id) return addToast({
      type: "error",
      title: "Adjuster update failed",
      description: "Failed to update adjuster. Adjuster ID is missing."
    });

    const payload: IAdjusterUpdatePayload = {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
    };
    const response = await updateEntity<IAdjusterUpdatePayload, IAdjuster>(`/v1/adjusters/${initialData.id}`, payload);
    if (response.error || !response.data) {
      addToast({
        type: "error",
        title: "Adjuster update failed",
        description: response.error || "Failed to update adjuster"
      });
      return;
    }
    addToast({
      type: "success",
      title: "Adjuster updated",
      description: "The adjuster has been updated successfully."
    })
    fetchAdjusters()
    onCancel();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 w-full">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input placeholder="Enter phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Enter email" type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form>
  )
}