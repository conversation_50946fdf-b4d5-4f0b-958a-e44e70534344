import { createContext, useCallback, useContext, useState, type ReactNode } from "react";
import { getFromApi, postToApi, putToApi, deleteFrom<PERSON>pi, type ApiResponse, patchToApi } from "@/lib/api";
import type { BusinessContextType, EntityResponse } from "@/context/types/BusinessTypes";
import type { IAdjuster, IAssignment, ICarrier, ICategory, IConsultant, IRole } from "@/types";

const BusinessContext = createContext<BusinessContextType | undefined>(undefined)
export function BusinessProvider({ children }: { children: ReactNode }) {
    const [assignments, setAssignments] = useState<IAssignment[]>([])
    const [adjusters, setAdjusters] = useState<IAdjuster[]>([])
    const [categories, setCategories] = useState<ICategory[]>([])
    const [consultants, setConsultants] = useState<IConsultant[]>([])
    const [carriers, setCarriers] = useState<ICarrier[]>([])
    const [roles, setRoles] = useState<IRole[]>([])

    const [loading, setLoading] = useState<{
        assignments: boolean;
        adjusters: boolean;
        categories: boolean;
        consultants: boolean;
        carriers: boolean
        roles: boolean;
    }>({
        assignments: false,
        adjusters: false,
        categories: false,
        consultants: false,
        carriers: false,
        roles: false,
    });

    const [contextError, setContextError] = useState<string | null>(null)

    const fetchAssignments = useCallback(async () => {
        setLoading(prev => ({ ...prev, assignments: true }));
        setContextError(null)

        try {
            const res: ApiResponse<{ results: IAssignment[] }> = await getFromApi<{ results: IAssignment[] }>(`/v1/assignments`, true)
            if (res.data != null) {
                console.log("Assignments", res.data.results);
                setAssignments(res.data.results);
            } else if (res.error) {
                setAssignments([]);
                setContextError(res.error.message);
            }
        } catch (error) {
            console.error("Error fetching Assignments:", error);
            setAssignments([]);
            setContextError("Failed to load assignments.");
        } finally {
            setLoading(prev => ({ ...prev, assignments: false }));
        }
    }, [])

    const fetchAdjusters = useCallback(async () => {
        setContextError(null)
        setLoading(prev => ({ ...prev, adjusters: true }));
        try {
            const res: ApiResponse<{ results: IAdjuster[] }> = await getFromApi<{ results: IAdjuster[] }>(`/v1/adjusters`, true)
            if (res.data != null) {
                console.log("Adjusters", res.data.results);
                setAdjusters(res.data.results);
            } else if (res.error) {
                setAdjusters([]);
                setContextError(res.error.message)
            }
        } catch (error) {
            console.error("Error fetching Adjusters:", error);
            setAdjusters([]);
            setContextError("Failed to load assignments.");
        } finally {
            setLoading(prev => ({ ...prev, adjusters: false }));
        }
    }, [])

    const fetchCategories = useCallback(async () => {
        setContextError(null)
        setLoading(prev => ({ ...prev, categories: true }));
        try {
            const res: ApiResponse<{ results: ICategory[] }> = await getFromApi<{ results: ICategory[] }>(`/v1/categories`, true)
            if (res.data != null) {
                console.log("Categories", res.data.results);
                setCategories(res.data.results);
            } else if (res.error) {
                setCategories([]);
                setContextError(res.error.message)
            }
        } catch (error) {
            console.error("Error fetching Categories: ", error)
            setCategories([])
            setContextError("Failed to load categories.")
        } finally {
            setLoading(prev => ({ ...prev, categories: false }));
        }
    }, [])

    const fetchConsultants = useCallback(async () => {
        setContextError(null)
        setLoading(prev => ({ ...prev, consultants: true }));
        try {
            const res: ApiResponse<{ results: IConsultant[] }> = await getFromApi<{ results: IConsultant[] }>(`/v1/consultants`, true)
            if (res.data != null) {
                console.log("Consultants", res.data.results);
                setConsultants(res.data.results);
            } else if (res.error) {
                setConsultants([]);
                setContextError(res.error.message)
            }
        } catch (error) {
            console.error("Error fetching Consultants: ", error)
            setConsultants([])
            setContextError("Failed to load consultants.")
        } finally {
            setLoading(prev => ({ ...prev, consultants: false }));
        }
    }, [])

    const fetchCarriers = useCallback(async () => {
        setContextError(null)
        setLoading(prev => ({ ...prev, carriers: true }));
        try {
            const res: ApiResponse<{ results: ICarrier[] }> = await getFromApi<{ results: ICarrier[] }>(`/v1/carriers`, true)
            if (res.data != null) {
                console.log("Carriers", res.data.results);
                setCarriers(res.data.results);
            } else if (res.error) {
                setCarriers([]);
                setContextError(res.error.message)
            }
        } catch (error) {
            console.error("Error fetching Carriers: ", error)
            setCarriers([])
            setContextError("Failed to load Carriers.")
        } finally {
            setLoading(prev => ({ ...prev, carriers: false }));
        }
    }, [])

    const fetchRoles = useCallback(async () => {
        setContextError(null)
        setLoading(prev => ({ ...prev, roles: true }));
        try {
            const res: ApiResponse<{ results: IRole[] }> = await getFromApi<{ results: IRole[] }>(`/v1/roles`, true)
            if (res.data != null) {
                console.log("Roles", res.data.results);
                setRoles(res.data.results);
            } else if (res.error) {
                setRoles([]);
                setContextError(res.error.message)
            }
        } catch (error) {
            console.error("Error fetching Roles: ", error)
            setRoles([])
            setContextError("Failed to load Roles.")
        } finally {
            setLoading(prev => ({ ...prev, roles: false }));
        }
    }, [])

    //HELPER FUNCTIONS
    async function fetchEntity<T>(url: string): Promise<EntityResponse<T>> {
        try {
            const response: ApiResponse<T> = await getFromApi<T>(url, true);

            if (response.data) {
                return { data: response.data, error: null };
            } else if (response.error) {
                return { data: null, error: response.error.message };
            } else {
                return { data: null, error: "Unknown error occurred" };
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to fetch entity";
            return { data: null, error: errorMessage };
        }
    }

    async function createEntity<D extends Record<string, any>, T>(url: string, data: D): Promise<EntityResponse<T>> {
        const { id: _, ...cleanedData } = data;
        const response = await postToApi<T>(url, cleanedData, true);
        if (!response.data || response.error) {
            const errorMessage = response.error?.message || "Failed to create entity";
            console.error(`Failed to create entity at ${url}:`, errorMessage);
            return { data: null, error: errorMessage };
        }

        return { data: response.data, error: null };
    }

    async function updateEntity<D extends Record<string, any>, T>(url: string, data: D): Promise<EntityResponse<T>> {
        const { id: _, ...cleanedData } = data;
        const response = await putToApi<T>(url, cleanedData, true);

        if (!response.data || response.error) {
            const errorMessage = response.error?.message || "Failed to update entity";
            console.error(`Failed to update entity at ${url}:`, errorMessage);
            return { data: null, error: errorMessage };
        }

        return { data: response.data, error: null };
    }

    async function patchEntity<D extends Record<string, any>, T>(url: string, data: D): Promise<EntityResponse<T>> {
        const response = await patchToApi<T>(url, data, true);

        if (!response.data || response.error) {
            const errorMessage = response.error?.message || "Failed to patch entity";
            console.error(`Failed to patch entity at ${url}:`, errorMessage);
            return { data: null, error: errorMessage };
        }

        return { data: response.data, error: null };
    }

    async function deleteEntity(url: string): Promise<EntityResponse<null>> {
        const response = await deleteFromApi(url, true);

        if (response.error) {
            const errorMessage = response.error?.message || "Failed to delete entity";
            console.error(`Failed to delete entity at ${url}:`, errorMessage);
            return { data: null, error: errorMessage };
        }

        return { data: null, error: null };
    }

    const clearError = useCallback(() => {
        setContextError(null);
    }, [])

    const contextValue: BusinessContextType = {
        entities: {
            assignments,
            adjusters,
            categories,
            consultants,
            carriers,
            roles,
        },
        loading,
        contextError,
        fetchActions: {
            fetchAdjusters,
            fetchAssignments,
            fetchCategories,
            fetchConsultants,
            fetchCarriers,
            fetchRoles,
            fetchEntity,
        },
        entityActions: {
            createEntity,
            updateEntity,
            deleteEntity,
            patchEntity,
        },
        clearError,
    };

    return (
        <BusinessContext.Provider value={contextValue}>
            {children}
        </BusinessContext.Provider>
    )
}

export const useBusinessContext = () => {
    const context = useContext(BusinessContext);
    if (context === undefined) {
        throw new Error("useBusinessContext must be used within a BusinessProvider");
    }
    return context;
};