
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ThemeToggle } from "@/components/custom-ui/ThemeToggle";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Menu, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useUserContext } from "@/context/UserContext";
import { useToast } from "@/components/custom-ui/toast";
import { Dialog, DialogContent, DialogTitle } from "../ui/dialog";
import { LoginForm } from "../home/<USER>";
import { SignupForm } from "../home/<USER>";

interface HeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
}

export function Header({ toggleSidebar, isSidebarOpen }: HeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "signup">("login");

  const navigate = useNavigate();
  const { user, logoutUser } = useUserContext();
  const { addToast } = useToast();

  const handleSwitchMode = () => {
    authMode === "login" ?
      setAuthMode("signup") :
      setAuthMode("login");
  };

  const handleLogout = async () => {
    try {
      await logoutUser();
      addToast({
        type: "success",
        title: "Logout successful",
        description: "You have been logged out."
      });
      navigate('/');
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  const authModalHandler = (mode: "login" | "signup") => {
    setAuthMode(mode);
    setAuthModalOpen(true);
  };

  return (
    <header className="border-b bg-background/95 backdrop-blur sticky top-0 z-40 w-full">
      <div className="container flex h-16 items-center justify-between py-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            id="sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle sidebar"
            className="transition-transform duration-300"
          >
            {isSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
          <a href="/" className="flex items-center space-x-2">
            <img src="/name_gray.png" alt="" className="h-10 w-full" />
          </a>
        </div>

        <div className="hidden md:flex items-center justify-center flex-1 px-4">
          <div className={cn(
            "relative w-full max-w-md transition-all duration-300",
            isSearchFocused ? "scale-105" : ""
          )}>
            <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8 bg-secondary"
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
            />
          </div>
        </div>

        <div className="flex items-center gap-4">
          {user == null &&
            <div className="hidden sm:flex gap-2">
              <Button variant="ghost" onClick={() => authModalHandler("login")}>
                Log in
              </Button>
              <Button variant="default" onClick={() => authModalHandler("signup")}>
                Sign up
              </Button>
            </div>
          }
          {user != null &&
            <div className="hidden sm:flex gap-2">
              <Button
                variant="ghost"
                onClick={() => navigate('/account')}
              >
                {user.email}
              </Button>
              <Button variant="ghost" onClick={handleLogout}>Logout</Button>
            </div>
          }
          <ThemeToggle />
        </div>
      </div>

      <Dialog open={authModalOpen} onOpenChange={setAuthModalOpen}>
        <DialogContent className="sm:max-w-[425px]" aria-describedby={undefined}>
          <DialogTitle className="text-center">
            {authMode === "login" ? "Log in" : "Sign up"}
          </DialogTitle>
          {authMode === "login" ? (
            <LoginForm
              onSuccess={() => setAuthModalOpen(false)}
              onSwitchToSignup={handleSwitchMode}
            />
          ) : (
            <SignupForm
              onSuccess={() => setAuthModalOpen(false)}
              onSwitchToLogin={handleSwitchMode}
            />
          )}
        </DialogContent>
      </Dialog>
    </header>
  );
}