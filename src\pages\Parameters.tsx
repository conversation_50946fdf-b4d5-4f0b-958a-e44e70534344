import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Modal } from '@/components/custom-ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/custom-ui/textarea';
import { Plus, Edit, Trash2, Lock, Unlock } from 'lucide-react';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
export const mockParameters: any[] = [
  {
    id: uuidv4(),
    key: "max_assignment_amount",
    name: "Maximum Assignment Amount",
    value: 1000000,
    type: "number",
    category: "Assignments",
    description: "Maximum amount for a single assignment",
    isEditable: true,
    validationRules: {
      min: 1000,
      max: 10000000,
      required: true
    },
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    key: "auto_assign_threshold",
    name: "Auto Assignment Threshold",
    value: 72,
    type: "number",
    category: "Workflow",
    description: "Hours before auto-assigning unassigned assignments",
    isEditable: true,
    validationRules: {
      min: 1,
      max: 168,
      required: true
    },
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];
export function Parameters() {
  const [parameters, setParameters] = useState<any[]>(mockParameters);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingParameter, setEditingParameter] = useState<any | null>(null);
  const [formData, setFormData] = useState<Partial<any>>({});

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'string':
        return '📝';
      case 'number':
        return '🔢';
      case 'boolean':
        return '✅';
      case 'json':
        return '📋';
      default:
        return '⚙️';
    }
  };

  const formatValue = (value: string | number | boolean, type: string) => {
    if (type === 'boolean') {
      return value ? 'True' : 'False';
    }
    if (type === 'number') {
      return Number(value).toLocaleString();
    }
    return String(value);
  };

  const columns: Column<any>[] = [
    {
      key: 'name',
      header: 'any Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
            <span className="text-sm">{getTypeIcon(row.type)}</span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground font-mono">{row.key}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
      render: (value) => (
        <Badge variant="secondary">{value}</Badge>
      )
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      render: (value) => (
        <Badge variant="outline" className="capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'value',
      header: 'Current Value',
      render: (value, row) => (
        <div className="font-mono text-sm">
          {formatValue(value, row.type)}
        </div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      render: (value) => (
        <span className="text-sm text-muted-foreground line-clamp-2">
          {value}
        </span>
      )
    },
    {
      key: 'isEditable',
      header: 'Editable',
      render: (value) => (
        <div className="flex items-center gap-2">
          {value ? (
            <Unlock className="h-4 w-4 text-green-600" />
          ) : (
            <Lock className="h-4 w-4 text-red-600" />
          )}
          <span className="text-sm">{value ? 'Yes' : 'No'}</span>
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
            disabled={!row.isEditable}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
            disabled={!row.isEditable}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingParameter(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (parameter: any) => {
    setEditingParameter(parameter);
    setFormData(parameter);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setParameters(parameters.filter(p => p.id !== id));
  };

  const handleSave = () => {
    if (editingParameter) {
      // Update existing parameter
      setParameters(parameters.map(p =>
        p.id === editingParameter.id ? { ...p, ...formData } : p
      ));
    } else {
      // Add new parameter
      const newParameter: any = {
        id: Date.now().toString(),
        key: formData.key || '',
        name: formData.name || '',
        value: formData.value || '',
        type: formData.type as 'string' | 'number' | 'boolean' | 'json' || 'string',
        category: formData.category || '',
        description: formData.description || '',
        isEditable: formData.isEditable ?? true,
        validationRules: formData.validationRules,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setParameters([...parameters, newParameter]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Parameters"
        description="Manage system configuration and business rules"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add any
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={parameters}
          columns={columns}
          searchPlaceholder="Search parameters..."
          onRowClick={(parameter) => console.log('View parameter:', parameter)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingParameter ? 'Edit any' : 'Add New any'}
        size="md"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingParameter ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">any Name</Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter parameter name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="key">Key</Label>
            <Input
              id="key"
              value={formData.key || ''}
              onChange={(e) => setFormData({ ...formData, key: e.target.value })}
              placeholder="Enter parameter key (e.g., max_claim_amount)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select
              value={formData.type || ''}
              onValueChange={(value) => setFormData({ ...formData, type: value as 'string' | 'number' | 'boolean' | 'json' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select parameter type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="string">String</SelectItem>
                <SelectItem value="number">Number</SelectItem>
                <SelectItem value="boolean">Boolean</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="value">Value</Label>
            <Input
              id="value"
              value={String(formData.value || '')}
              onChange={(e) => {
                let value: string | number | boolean = e.target.value;
                if (formData.type === 'number') {
                  value = parseFloat(e.target.value) || 0;
                } else if (formData.type === 'boolean') {
                  value = e.target.value === 'true';
                }
                setFormData({ ...formData, value });
              }}
              placeholder="Enter parameter value"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Input
              id="category"
              value={formData.category || ''}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              placeholder="Enter category (e.g., Claims, Workflow)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter parameter description"
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isEditable"
              checked={formData.isEditable ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isEditable: checked })}
            />
            <Label htmlFor="isEditable">Editable</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
