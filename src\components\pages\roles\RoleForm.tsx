import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/custom-ui/toast";
import { useBusinessContext } from "@/context/BusinessContext";
import { useUserContext } from "@/context/UserContext";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import type { IRole, IRoleCreatePayload, IRoleUpdatePayload } from "@/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export const roleFormSchema = z.object({
	id: z.number().optional(),
	name: z.string().min(1, "Name is required"),
	value: z.number().min(0, "Value must be a positive number"),
})

interface RoleFormProps {
	initialData?: IRole;
	mode?: "create" | "edit";
	onCancel: () => void;
}

export function RoleForm({ initialData, onCancel, mode = "create" }: RoleFormProps) {
	const { user } = useUserContext();
	const {
		entityActions: {
			createEntity,
			updateEntity
		},
		fetchActions: {
			fetchRoles
		}
	} = useBusinessContext()
	const { addToast } = useToast();

	const form = useForm<z.infer<typeof roleFormSchema>>({
		resolver: zodResolver(roleFormSchema),
		defaultValues: {
			id: initialData?.id || 0,
			name: initialData?.name || "",
			value: initialData?.value || 0,
		}
	})

	const handleSubmit = async (data: z.infer<typeof roleFormSchema>) => {
		if (!user) return addToast({
			type: "error",
			title: "Role creation failed",
			description: "You must be logged in to create a role."
		});

		if (mode === "create") {
			handleCreate(data);
		} else {
			handleEdit(data);
		}
	}

	const handleCreate = async (data: z.infer<typeof roleFormSchema>) => {
		const payload: IRoleCreatePayload = {
			name: data.name,
			value: data.value
		}

		const response = await createEntity<IRoleCreatePayload, IRole>("/v1/roles", payload)
		if (response.error) return addToast({
			type: "error",
			title: "Role creation failed",
			description: response.error || "Failed to create role"
		})
		addToast({
			type: "success",
			title: "Role created",
			description: "The role has been created successfully."
		})
		fetchRoles()
		onCancel();
	}

	const handleEdit = async (data: z.infer<typeof roleFormSchema>) => {
		if (!initialData || !data.id) return addToast({
			type: "error",
			title: "Role update failed",
			description: "Failed to update role. Role ID is missing."
		});

		const payload: IRoleUpdatePayload = {
			id: data.id,
			name: data.name,
			value: data.value,
		}
		const response = await updateEntity<IRoleUpdatePayload, IRole>(`/v1/roles/${initialData.id}`, payload)
		if (response.error) return addToast({
			type: "error",
			title: "Role update failed",
			description: response.error || "Failed to update role"
		})
		addToast({
			type: "success",
			title: "Role updated",
			description: "The role has been updated successfully."
		})
		fetchRoles()
		onCancel();
	}

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Name</FormLabel>
							<FormControl>
								<Input placeholder="Enter role name" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="value"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Value</FormLabel>
							<FormControl>
								<Input 
									type="number" 
									placeholder="Enter role value" 
									{...field}
									onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<div className="flex justify-end gap-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
					>
						Cancel
					</Button>
					<Button
						type="submit"
					>
						Save
					</Button>
				</div>
			</form>
		</Form>
	)
}
